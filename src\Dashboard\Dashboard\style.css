.content {
  align-items: center;
  background-color: #fbf9f4;
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 1280px;
}

.content .header {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  position: relative;
  width: 100%;
}

.content .content-wrapper {
  align-items: center;
  align-self: stretch;
  background-color: #0e5447;
  display: flex;
  gap: 691px;
  height: 57px;
  padding: 16px 48px;
  position: relative;
  width: 100%;
}

.content .div {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 20px;
  position: relative;
}

.content .depository-trust {
  height: 25px;
  overflow: hidden;
  position: relative;
  width: 65px;
}

.content .text-wrapper {
  color: #ffffff;
  font-family: "Fugaz One-Regular", Helvetica;
  font-size: 24px;
  font-weight: 400;
  left: 1px;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  top: -5px;
  width: 68px;
}

.content .line {
  height: 20px !important;
  object-fit: cover !important;
  position: relative !important;
  width: 1px !important;
}

.content .lorem-ipsum {
  color: #ffffff;
  flex: 1;
  font-family: var(--application-header-font-family);
  font-size: var(--application-header-font-size);
  font-style: var(--application-header-font-style);
  font-weight: var(--application-header-font-weight);
  letter-spacing: var(--application-header-letter-spacing);
  line-height: var(--application-header-line-height);
  position: relative;
}

.content .tool-bar-icons {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  justify-content: flex-end;
  position: relative;
}

.content .frame {
  align-items: flex-end;
  display: flex;
  flex-direction: column;
  gap: 10px;
  justify-content: center;
  position: relative;
  width: 209px;
}

.content .tool-bar-icons-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 16px;
  justify-content: flex-end;
  position: relative;
}

.content .search {
  background: none;
  border: none;
  color: #ffffff;
  font-family: "Font Awesome 5 Pro-Regular", Helvetica;
  font-size: 20px;
  font-weight: 400;
  height: 20px;
  letter-spacing: 0;
  line-height: normal;
  padding: 0;
  padding-left: 0;
  position: relative;
  text-align: center;
  white-space: nowrap;
  width: 20px;
}

.content .div-wrapper {
  height: 20px;
  position: relative;
  width: 20px;
}

.content .text-wrapper-2 {
  color: #ffffff;
  font-family: "Font Awesome 5 Pro-Regular", Helvetica;
  font-size: 20px;
  font-weight: 400;
  left: 0;
  letter-spacing: 0;
  line-height: normal;
  position: absolute;
  text-align: center;
  top: -2px;
  white-space: nowrap;
}

.content .menu {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  position: relative;
}

.content .menu-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  position: relative;
}

.content .th {
  color: #ffffff;
  font-family: "Font Awesome 5 Pro-Regular", Helvetica;
  font-size: 20px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  text-align: center;
  white-space: nowrap;
  width: fit-content;
}

.content .logout {
  height: 14.97px;
  position: relative;
  width: 20px;
}

.content .logout-2 {
  height: 15px;
  position: relative;
  width: 20px;
}

.content .component {
  align-items: flex-start;
  align-self: stretch;
  background-color: #ffffff;
  display: flex;
  gap: 40px;
  height: 41px;
  padding: 0px 48px;
  position: relative;
  width: 100%;
}

.content .tab-item {
  align-items: center;
  border-bottom-style: solid;
  border-bottom-width: 3px;
  border-color: #ffc454;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-content: center;
  padding: 12.5px 0px;
  position: relative;
}

.content .text-wrapper-3 {
  color: #212121;
  font-family: var(--tab-header-font-family);
  font-size: var(--tab-header-font-size);
  font-style: var(--tab-header-font-style);
  font-weight: var(--tab-header-font-weight);
  letter-spacing: var(--tab-header-letter-spacing);
  line-height: var(--tab-header-line-height);
  margin-top: -3.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .tab-item-2 {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-content: center;
  padding: 12.5px 0px;
  position: relative;
}

.content .NAVIGATION-ONE {
  color: #616161;
  font-family: var(--tab-header-font-family);
  font-size: var(--tab-header-font-size);
  font-style: var(--tab-header-font-style);
  font-weight: var(--tab-header-font-weight);
  letter-spacing: var(--tab-header-letter-spacing);
  line-height: var(--tab-header-line-height);
  margin-top: -3.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .simple-alerts {
  align-items: center;
  background-color: #f8fcfb;
  border: 1px solid;
  border-color: #657b75;
  border-radius: 4px;
  display: flex;
  flex: 0 0 auto;
  gap: 10px;
  padding: 16px 20px;
  position: relative;
  width: 1184px;
}

.content .a-simple-info-alert-wrapper {
  align-items: center;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 12px;
  position: relative;
}

.content .a-simple-info-alert {
  color: #212121;
  font-family: var(--heading-h4-font-family);
  font-size: var(--heading-h4-font-size);
  font-style: var(--heading-h4-font-style);
  font-weight: var(--heading-h4-font-weight);
  letter-spacing: var(--heading-h4-letter-spacing);
  line-height: var(--heading-h4-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .asset {
  height: 11.32px;
  position: relative;
  width: 11.32px;
}

.content .KP-is {
  align-items: flex-start;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 12px;
  position: relative;
  width: 1184px;
}

.content .test-DASH {
  align-self: stretch;
  color: #0e5447;
  font-family: "Roboto-Medium", Helvetica;
  font-size: 18px;
  font-weight: 500;
  height: 21px;
  letter-spacing: 0;
  line-height: normal;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
}

.content .cards {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 20px;
  position: relative;
  width: 100%;
}

.content .group {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px;
  box-shadow: var(--card-shadow);
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 90px;
  justify-content: center;
  padding: 10px;
  position: relative;
  width: 180px;
}

.content .group-2 {
  height: 46px;
  position: relative;
  width: 156px;
}

.content .total-alerts {
  color: #616161;
  font-family: var(--general-form-elements-medium-font-family);
  font-size: var(--general-form-elements-medium-font-size);
  font-style: var(--general-form-elements-medium-font-style);
  font-weight: var(--general-form-elements-medium-font-weight);
  left: 0;
  letter-spacing: var(--general-form-elements-medium-letter-spacing);
  line-height: var(--general-form-elements-medium-line-height);
  position: absolute;
  text-align: center;
  top: 31px;
  width: 152px;
}

.content .element {
  color: #e86a3a;
  font-family: var(--heading-h1-font-family);
  font-size: var(--heading-h1-font-size);
  font-style: var(--heading-h1-font-style);
  font-weight: var(--heading-h1-font-weight);
  left: 63px;
  letter-spacing: var(--heading-h1-letter-spacing);
  line-height: var(--heading-h1-line-height);
  position: absolute;
  text-align: center;
  top: 0;
  white-space: nowrap;
}

.content .element-2 {
  color: #e86a3a;
  font-family: var(--heading-h1-font-family);
  font-size: var(--heading-h1-font-size);
  font-style: var(--heading-h1-font-style);
  font-weight: var(--heading-h1-font-weight);
  left: 57px;
  letter-spacing: var(--heading-h1-letter-spacing);
  line-height: var(--heading-h1-line-height);
  position: absolute;
  text-align: center;
  top: 0;
  white-space: nowrap;
}

.content .group-wrapper {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px;
  box-shadow: var(--card-shadow);
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 90px;
  justify-content: center;
  padding: 10px;
  position: relative;
  width: 184px;
}

.content .chartrow {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 20px;
  justify-content: center;
  position: relative;
  width: 100%;
}

.content .chart {
  align-items: center;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 2px 4px #00000073;
  display: flex;
  flex-direction: column;
  height: 340px;
  justify-content: space-between;
  padding: 16px;
  position: relative;
  width: 380px;
}

.content .title {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 10px;
  position: relative;
  width: 100%;
}

.content .heading {
  align-items: center;
  display: inline-flex;
  gap: 10px;
  height: 21px;
  justify-content: center;
  position: relative;
}

.content .text-wrapper-4 {
  color: #0e5447;
  font-family: var(--heading-h3-font-family);
  font-size: var(--heading-h3-font-size);
  font-style: var(--heading-h3-font-style);
  font-weight: var(--heading-h3-font-weight);
  letter-spacing: var(--heading-h3-letter-spacing);
  line-height: var(--heading-h3-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .dropdowns {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 12px;
  justify-content: flex-end;
  margin-top: -0.33px;
  position: relative;
  width: 100%;
}

.content .box-header-wrapper {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
  width: 44px;
}

.content .box-header {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 2px;
  justify-content: flex-end;
  position: relative;
  width: 100%;
}

.content .default {
  color: #212121;
  font-family: var(--general-form-elements-medium-font-family);
  font-size: var(--general-form-elements-medium-font-size);
  font-style: var(--general-form-elements-medium-font-style);
  font-weight: var(--general-form-elements-medium-font-weight);
  letter-spacing: var(--general-form-elements-medium-letter-spacing);
  line-height: var(--general-form-elements-medium-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .frame-2 {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  height: 36px;
  padding: 10px 12px;
  position: relative;
}

.content .select {
  color: #616161;
  font-family: var(--general-default-body-text-font-family);
  font-size: var(--general-default-body-text-font-size);
  font-style: var(--general-default-body-text-font-style);
  font-weight: var(--general-default-body-text-font-weight);
  letter-spacing: var(--general-default-body-text-letter-spacing);
  line-height: var(--general-default-body-text-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .frame-3 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 10px;
  position: relative;
  width: 12px;
}

.content .text-wrapper-5 {
  color: #0e5447;
  font-family: "Font Awesome 5 Pro-Regular", Helvetica;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: normal;
  margin-right: -2.00px;
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .group-3 {
  height: 220px;
  margin-top: -0.33px;
  position: relative;
  width: 220px;
}

.content .group-4 {
  height: 220px;
}

.content .group-5 {
  height: 220px;
  width: 220px;
}

.content .overlap-group {
  height: 165px;
  left: 30px;
  position: relative;
  top: 24px;
  width: 159px;
}

.content .ellipse {
  background-color: #90b0aa;
  border-radius: 79.66px;
  height: 159px;
  left: 0;
  position: absolute;
  top: 6px;
  width: 159px;
}

.content .ellipse-3 {
  height: 100px !important;
  left: 74px !important;
  position: absolute !important;
  top: 0 !important;
  width: 71px !important;
}

.content .text {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 4px;
  margin-top: -0.33px;
  position: relative;
  width: 100%;
}

.content .text-2 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-content: center;
  position: relative;
  width: 100%;
}

.content .quisque-rutrum {
  color: transparent;
  font-family: "Roboto-Regular", Helvetica;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 12px;
  margin-top: -1.00px;
  position: relative;
  width: 106px;
}

.content .span {
  color: #616161;
  line-height: 0.1px;
}

.content .text-wrapper-6 {
  color: #616161;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
}

.content .aenean-imperdiet {
  color: transparent;
  font-family: "Roboto-Regular", Helvetica;
  font-size: 12px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 12px;
  margin-top: -1.00px;
  position: relative;
  width: 122px;
}

.content .key {
  align-items: flex-start;
  display: flex;
  gap: 16px;
  height: 14px;
  position: relative;
  width: 113.4px;
}

.content .frame-4 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 5px;
  position: relative;
}

.content .ellipse-2 {
  background-color: #90b0aa;
  border-radius: 6.5px;
  height: 13px;
  position: relative;
  width: 13px;
}

.content .text-wrapper-7 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .ellipse-4 {
  background-color: #e7eeed;
  border-radius: 6.5px;
  height: 13px;
  position: relative;
  width: 13px;
}

.content .overlap-group-2 {
  height: 165px;
  left: 30px;
  position: relative;
  top: 24px;
  width: 165px;
}

.content .ellipse-5 {
  background-color: #ff7540;
  border-radius: 79.66px;
  height: 159px;
  left: 0;
  position: absolute;
  top: 6px;
  width: 159px;
}

.content .ellipse-3-instance {
  height: 151px !important;
  left: 74px !important;
  position: absolute !important;
  top: 0 !important;
  width: 92px !important;
}

.content .frame-5 {
  align-items: flex-start;
  display: flex;
  gap: 16px;
  height: 14px;
  position: relative;
  width: 81.57px;
}

.content .ellipse-6 {
  background-color: #ff7540;
  border-radius: 6.5px;
  height: 13px;
  position: relative;
  width: 13px;
}

.content .frame-6 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 5px;
  margin-right: -2.43px;
  position: relative;
}

.content .ellipse-7 {
  background-color: #ffd4c4;
  border-radius: 6.5px;
  height: 13px;
  position: relative;
  width: 13px;
}

.content .overlap-group-3 {
  height: 171px;
  left: 30px;
  position: relative;
  top: 24px;
  width: 165px;
}

.content .ellipse-2-instance {
  height: 159px !important;
  left: 0 !important;
  position: absolute !important;
  top: 6px !important;
  width: 159px !important;
}

.content .icon-instance-node {
  height: 171px !important;
  left: 2px !important;
  position: absolute !important;
  top: 0 !important;
  width: 163px !important;
}

.content .frame-7 {
  align-items: flex-start;
  display: flex;
  gap: 16px;
  height: 14px;
  position: relative;
  width: 99px;
}

.content .ellipse-8 {
  background-color: #ffb933;
  border-radius: 6.5px;
  height: 13px;
  position: relative;
  width: 13px;
}

.content .ellipse-9 {
  background-color: #ffd78a;
  border-radius: 6.5px;
  height: 13px;
  position: relative;
  width: 13px;
}

.content .row {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 20px;
  position: relative;
}

.content .col {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 20px;
  justify-content: center;
  position: relative;
}

.content .chart-2 {
  align-items: center;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 26px;
  padding: 13.79px 9.94px 13px 29px;
  position: relative;
  width: 780px;
}

.content .cards-2 {
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px;
  box-shadow: 0px 2px 4px #00000073;
  height: 340px;
  left: 0;
  position: absolute;
  top: 0;
  width: 780px;
}

.content .frame-8 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  justify-content: space-between;
  position: relative;
  width: 100%;
}

.content .the-quick-brown-fox-wrapper {
  align-items: center;
  display: flex;
  gap: 10px;
  height: 29px;
  justify-content: center;
  position: relative;
  width: 168px;
}

.content .the-quick-brown-fox {
  color: #0e5447;
  font-family: var(--heading-h3-font-family);
  font-size: var(--heading-h3-font-size);
  font-style: var(--heading-h3-font-style);
  font-weight: var(--heading-h3-font-weight);
  letter-spacing: var(--heading-h3-letter-spacing);
  line-height: var(--heading-h3-line-height);
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .frame-9 {
  align-items: flex-start;
  display: flex;
  padding: 0px 3.81e-06px 9.24px 95.53px;
  position: relative;
  width: 145.07px;
}

.content .dropdowns-2 {
  align-items: center;
  display: flex;
  gap: 12px;
  left: 0;
  position: absolute;
  top: 0;
  width: 144px;
}

.content .frame-10 {
  align-items: center;
  background-color: #ffffff;
  border: 1px solid;
  border-color: #bdbdbd;
  border-radius: 4px;
  display: flex;
  flex: 1;
  flex-grow: 1;
  gap: 10px;
  height: 36px;
  padding: 10px 12px;
  position: relative;
}

.content .select-2 {
  color: #616161;
  flex: 1;
  font-family: var(--general-default-body-text-font-family);
  font-size: var(--general-default-body-text-font-size);
  font-style: var(--general-default-body-text-font-style);
  font-weight: var(--general-default-body-text-font-weight);
  letter-spacing: var(--general-default-body-text-letter-spacing);
  line-height: var(--general-default-body-text-line-height);
  margin-top: -1.00px;
  position: relative;
}

.content .group-6 {
  height: 28.97px;
  position: relative;
  width: 49.54px;
}

.content .frame-11 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 13px;
  padding: 0px 44.62px;
  position: relative;
  width: 100%;
}

.content .frame-12 {
  align-items: flex-start;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  padding: 57px 469.38px 134px 106.44px;
  position: relative;
  width: 100%;
}

.content .overlap-group-wrapper {
  height: 222px;
  left: 0;
  position: absolute;
  top: 0;
  width: 652px;
}

.content .overlap-group-4 {
  height: 222px;
  position: relative;
}

.content .line-36 {
  height: 1px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 0 !important;
  width: 652px !important;
}

.content .line-36-instance {
  height: 1px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 66px !important;
  width: 652px !important;
}

.content .line-2 {
  height: 1px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 99px !important;
  width: 652px !important;
}

.content .line-3 {
  height: 1px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 132px !important;
  width: 652px !important;
}

.content .line-4 {
  height: 1px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 166px !important;
  width: 652px !important;
}

.content .line-37 {
  height: 1px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 199px !important;
  width: 652px !important;
}

.content .line-33 {
  height: 1px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 32px !important;
  width: 652px !important;
}

.content .group-7 {
  height: 221px;
  left: 38px;
  position: absolute;
  top: 0;
  width: 136px;
}

.content .group-8 {
  height: 199px;
  left: 0;
  position: absolute;
  top: 0;
  width: 130px;
}

.content .group-9 {
  height: 199px;
  position: relative;
}

.content .rectangle {
  background-color: #e89800;
  height: 199px;
  left: 0;
  position: absolute;
  top: 0;
  width: 40px;
}

.content .rectangle-2 {
  background-color: #ffb933;
  height: 75px;
  left: 45px;
  position: absolute;
  top: 124px;
  width: 40px;
}

.content .rectangle-3 {
  background-color: #ffd78a;
  height: 108px;
  left: 90px;
  position: absolute;
  top: 91px;
  width: 40px;
}

.content .text-wrapper-8 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 58px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 207px;
  white-space: nowrap;
  width: 16px;
}

.content .text-wrapper-9 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 10px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 207px;
  white-space: nowrap;
  width: 18px;
}

.content .text-wrapper-10 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 103px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 207px;
  white-space: nowrap;
  width: 17px;
}

.content .group-10 {
  height: 197px;
  left: 253px;
  position: absolute;
  top: 25px;
  width: 130px;
}

.content .group-11 {
  height: 197px;
}

.content .group-12 {
  height: 197px;
  position: relative;
  width: 136px;
}

.content .rectangle-4 {
  background-color: #0d4c41;
  height: 104px;
  left: 0;
  position: absolute;
  top: 70px;
  width: 40px;
}

.content .text-wrapper-11 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 54px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 183px;
  white-space: nowrap;
  width: 16px;
}

.content .text-wrapper-12 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 10px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 183px;
  white-space: nowrap;
  width: 18px;
}

.content .text-wrapper-13 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 100px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 183px;
  white-space: nowrap;
  width: 17px;
}

.content .rectangle-5 {
  background-color: #90b0aa;
  height: 75px;
  left: 45px;
  position: absolute;
  top: 99px;
  width: 40px;
}

.content .rectangle-6 {
  background-color: #e7eeed;
  height: 174px;
  left: 90px;
  position: absolute;
  top: 0;
  width: 40px;
}

.content .group-13 {
  height: 202px;
  left: 468px;
  position: absolute;
  top: 19px;
  width: 130px;
}

.content .group-14 {
  height: 202px;
}

.content .group-15 {
  height: 202px;
  position: relative;
  width: 136px;
}

.content .rectangle-7 {
  background-color: #b5532d;
  height: 49px;
  left: 0;
  position: absolute;
  top: 131px;
  width: 40px;
}

.content .rectangle-8 {
  background-color: #ff7540;
  height: 180px;
  left: 45px;
  position: absolute;
  top: 0;
  width: 40px;
}

.content .text-wrapper-14 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 58px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 188px;
  white-space: nowrap;
  width: 16px;
}

.content .text-wrapper-15 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 14px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 188px;
  white-space: nowrap;
  width: 18px;
}

.content .text-wrapper-16 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 105px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 188px;
  white-space: nowrap;
  width: 17px;
}

.content .rectangle-9 {
  background-color: #ffd4c4;
  height: 99px;
  left: 90px;
  position: absolute;
  top: 81px;
  width: 40px;
}

.content .tooltips {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  position: relative;
}

.content .customized {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  flex-direction: column;
  position: relative;
}

.content .frame-13 {
  align-items: center;
  background-color: #657b75;
  border-radius: 4px;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 8px;
  justify-content: center;
  padding: 5px 12px;
  position: relative;
}

.content .label-wrapper {
  align-items: center;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  justify-content: center;
  position: relative;
}

.content .label {
  color: #ffffff;
  font-family: var(--general-small-label-font-family);
  font-size: var(--general-small-label-font-size);
  font-style: var(--general-small-label-font-style);
  font-weight: var(--general-small-label-font-weight);
  letter-spacing: var(--general-small-label-letter-spacing);
  line-height: var(--general-small-label-line-height);
  margin-top: -1.00px;
  position: relative;
  text-align: center;
  white-space: nowrap;
  width: fit-content;
}

.content .polygon {
  height: 3px !important;
  margin-top: -1px !important;
  position: relative !important;
  width: 6.06px !important;
}

.content .group-16 {
  height: 14px;
  position: relative;
  width: 374px;
}

.content .group-17 {
  height: 14px;
  position: relative;
}

.content .group-18 {
  height: 14px;
  left: 0;
  position: absolute;
  top: 0;
  width: 241px;
}

.content .ellipse-10 {
  background-color: #0d4c41;
  border-radius: 6.5px;
  height: 13px;
  left: 140px;
  position: absolute;
  top: 0;
  width: 13px;
}

.content .ellipse-11 {
  background-color: #90b0aa;
  border-radius: 6.5px;
  height: 13px;
  left: 158px;
  position: absolute;
  top: 0;
  width: 13px;
}

.content .ellipse-12 {
  background-color: #e7eeed;
  border-radius: 6.5px;
  height: 13px;
  left: 176px;
  position: absolute;
  top: 0;
  width: 13px;
}

.content .text-wrapper-17 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 194px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 0;
  white-space: nowrap;
}

.content .group-19 {
  height: 14px;
  left: 0;
  position: absolute;
  top: 0;
  width: 101px;
}

.content .ellipse-13 {
  background-color: #e89800;
  border-radius: 6.5px;
  height: 13px;
  left: 0;
  position: absolute;
  top: 0;
  width: 13px;
}

.content .ellipse-14 {
  background-color: #ffb933;
  border-radius: 6.5px;
  height: 13px;
  left: 18px;
  position: absolute;
  top: 0;
  width: 13px;
}

.content .ellipse-15 {
  background-color: #ffd78a;
  border-radius: 6.5px;
  height: 13px;
  left: 36px;
  position: absolute;
  top: 0;
  width: 13px;
}

.content .text-wrapper-18 {
  color: #212121;
  font-family: var(--general-small-text-font-family);
  font-size: var(--general-small-text-font-size);
  font-style: var(--general-small-text-font-style);
  font-weight: var(--general-small-text-font-weight);
  left: 54px;
  letter-spacing: var(--general-small-text-letter-spacing);
  line-height: var(--general-small-text-line-height);
  position: absolute;
  top: 0;
  white-space: nowrap;
}

.content .group-20 {
  height: 14px;
  left: 275px;
  position: absolute;
  top: 0;
  width: 101px;
}

.content .ellipse-16 {
  background-color: #b5532d;
  border-radius: 6.5px;
  height: 13px;
  left: 0;
  position: absolute;
  top: 0;
  width: 13px;
}

.content .ellipse-17 {
  background-color: #ff7540;
  border-radius: 6.5px;
  height: 13px;
  left: 18px;
  position: absolute;
  top: 0;
  width: 13px;
}

.content .ellipse-18 {
  background-color: #ffd4c4;
  border-radius: 6.5px;
  height: 13px;
  left: 36px;
  position: absolute;
  top: 0;
  width: 13px;
}

.content .group-21 {
  height: 220px;
  position: relative;
  width: 220px;
}

.content .overlap-group-5 {
  height: 171px;
  left: 24px;
  position: relative;
  top: 24px;
  width: 169px;
}

.content .ellipse-19 {
  height: 159px !important;
  left: 6px !important;
  position: absolute !important;
  top: 6px !important;
  width: 159px !important;
}

.content .ellipse-20 {
  height: 171px !important;
  left: 0 !important;
  position: absolute !important;
  top: 0 !important;
  width: 169px !important;
}

.content .text-3 {
  align-items: center;
  align-self: stretch;
  display: flex;
  flex: 0 0 auto;
  flex-direction: column;
  gap: 4px;
  position: relative;
  width: 100%;
}

.content .frame-14 {
  align-items: flex-start;
  display: inline-flex;
  gap: 16px;
  height: 14px;
  position: relative;
}

.content .ellipse-21 {
  background-color: #dfd5c5;
  border-radius: 6.5px;
  height: 13px;
  position: relative;
  width: 13px;
}

.content .ellipse-22 {
  background-color: #f5ead9;
  border-radius: 6.5px;
  height: 13px;
  position: relative;
  width: 13px;
}

.content .widget-col {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  width: 380px;
}

.content .widget-card {
  align-items: flex-start;
  align-self: stretch;
  background-color: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 2px 4px #00000073;
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 160.5px;
  padding: 16px;
  position: relative;
  width: 100%;
}

.content .frame-15 {
  align-items: flex-end;
  display: flex;
  flex: 0 0 auto;
  gap: 19.35px;
  position: relative;
  width: 233.25px;
}

.content .frame-16 {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 10.5px;
  height: 126px;
  position: relative;
}

.content .text-wrapper-19 {
  align-self: stretch;
  color: #0e5447;
  font-family: var(--heading-h3-font-family);
  font-size: var(--heading-h3-font-size);
  font-style: var(--heading-h3-font-style);
  font-weight: var(--heading-h3-font-weight);
  letter-spacing: var(--heading-h3-letter-spacing);
  line-height: var(--heading-h3-line-height);
  margin-top: -1.00px;
  position: relative;
}

.content .AWQ {
  align-self: stretch;
  color: transparent;
  font-family: "Roboto-Bold", Helvetica;
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 13px;
  position: relative;
}

.content .text-wrapper-20 {
  color: #616161;
  font-family: var(--utility-menu-option-text-font-family);
  font-size: var(--utility-menu-option-text-font-size);
  font-style: var(--utility-menu-option-text-font-style);
  font-weight: var(--utility-menu-option-text-font-weight);
  letter-spacing: var(--utility-menu-option-text-letter-spacing);
  line-height: var(--utility-menu-option-text-line-height);
}

.content .text-wrapper-21 {
  align-self: stretch;
  color: #616161;
  font-family: var(--general-form-elements-medium-font-family);
  font-size: var(--general-form-elements-medium-font-size);
  font-style: var(--general-form-elements-medium-font-style);
  font-weight: var(--general-form-elements-medium-font-weight);
  letter-spacing: var(--general-form-elements-medium-letter-spacing);
  line-height: var(--general-form-elements-medium-line-height);
  position: relative;
}

.content .frame-17 {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  gap: 11.33px;
  position: relative;
  width: 56.02px;
}

.content .totals {
  align-self: stretch;
  color: transparent;
  font-family: "Roboto-Bold", Helvetica;
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 13px;
  margin-top: -1.00px;
  position: relative;
}

.content .p {
  align-self: stretch;
  color: transparent;
  font-family: "Roboto-Regular", Helvetica;
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 13px;
  position: relative;
}

.content .text-wrapper-22 {
  color: #e86a3a;
  line-height: 0.1px;
}

.content .text-wrapper-23 {
  color: #e86a3a;
  font-family: var(--general-form-elements-medium-font-family);
  font-size: var(--general-form-elements-medium-font-size);
  font-style: var(--general-form-elements-medium-font-style);
  font-weight: var(--general-form-elements-medium-font-weight);
  letter-spacing: var(--general-form-elements-medium-letter-spacing);
  line-height: var(--general-form-elements-medium-line-height);
}

.content .frame-18 {
  align-items: flex-start;
  display: flex;
  flex: 0 0 auto;
  gap: 19.35px;
  position: relative;
  width: 233.25px;
}

.content .frame-19 {
  align-items: flex-start;
  display: flex;
  flex: 1;
  flex-direction: column;
  flex-grow: 1;
  gap: 10.5px;
  position: relative;
}

.content .span-wrapper {
  align-self: stretch;
  color: #616161;
  font-family: "Roboto-Bold", Helvetica;
  font-size: 13px;
  font-weight: 400;
  letter-spacing: 0;
  line-height: 13px;
  margin-top: -1.00px;
  position: relative;
}

.content .text-wrapper-24 {
  font-family: var(--utility-menu-option-text-font-family);
  font-size: var(--utility-menu-option-text-font-size);
  font-style: var(--utility-menu-option-text-font-style);
  font-weight: var(--utility-menu-option-text-font-weight);
  letter-spacing: var(--utility-menu-option-text-letter-spacing);
  line-height: var(--utility-menu-option-text-line-height);
}

.content .element-3 {
  align-self: stretch;
  color: #e86a3a;
  font-family: var(--general-form-elements-medium-font-family);
  font-size: var(--general-form-elements-medium-font-size);
  font-style: var(--general-form-elements-medium-font-style);
  font-weight: var(--general-form-elements-medium-font-weight);
  letter-spacing: var(--general-form-elements-medium-letter-spacing);
  line-height: var(--general-form-elements-medium-line-height);
  position: relative;
}

.content .component-2 {
  background-color: #f5ead9;
  flex: 0 0 auto;
  position: relative;
  width: 1280px;
}

.content .frame-20 {
  align-items: center;
  display: inline-flex;
  gap: 12px;
  justify-content: center;
  left: 470px;
  position: relative;
  top: 11px;
}

.content .frame-21 {
  align-items: flex-start;
  display: inline-flex;
  flex: 0 0 auto;
  gap: 10px;
  padding: 0px 0px 0px 12px;
  position: relative;
}

.content .text-wrapper-25 {
  color: #0e5447;
  font-family: var(--footer-hyperlink-font-family);
  font-size: var(--footer-hyperlink-font-size);
  font-style: var(--footer-hyperlink-font-style);
  font-weight: var(--footer-hyperlink-font-weight);
  letter-spacing: var(--footer-hyperlink-letter-spacing);
  line-height: var(--footer-hyperlink-line-height);
  margin-top: -1.00px;
  position: relative;
  white-space: nowrap;
  width: fit-content;
}

.content .line-1 {
  height: 16px !important;
  left: 0 !important;
  object-fit: cover !important;
  position: absolute !important;
  top: 0 !important;
  width: 1px !important;
}
